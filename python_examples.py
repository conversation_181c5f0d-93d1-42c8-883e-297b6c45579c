# 1. 元组示例
# 元组是不可变的序列，通常用于存储不可修改的数据
coordinates = (10, 20)
print(f"Coordinates: {coordinates}")

# 元组可以存储任意数量的数据
single_element = (42,)  # 单个元素的元组需要加逗号
multiple_elements = (1, 2, 3, 4, 5)
mixed_types = ("Alice", 30, True, 3.14)

# 元组常用于：
# 1. 返回多个值
def get_user_info():
    return "<PERSON>", 30, "New York"

name, age, city = get_user_info()
print(f"Name: {name}, Age: {age}, City: {city}")

# 2. 作为字典的键（因为不可变）
locations = {
    (40.7128, -74.0060): "New York",
    (51.5074, -0.1278): "London"
}
print(f"Location at (40.7128, -74.0060): {locations[(40.7128, -74.0060)]}")

# 3. 保护数据不被修改
constants = (3.14159, 2.71828)
# constants[0] = 3.14  # 这会报错，因为元组不可变

# 4. 用于解包
x, y = coordinates
print(f"x: {x}, y: {y}")

# 5. 作为函数的参数和返回值
def min_max(numbers):
    return min(numbers), max(numbers)

values = (5, 2, 8, 1, 9)
min_val, max_val = min_max(values)
print(f"Min: {min_val}, Max: {max_val}")

# 元组操作
# 访问元素
print(f"First element: {multiple_elements[0]}")
# 切片
print(f"Slice: {multiple_elements[1:3]}")
# 长度
print(f"Length: {len(multiple_elements)}")
# 连接
combined = multiple_elements + (6, 7)
print(f"Combined: {combined}")

# 2. 字典与JSON
# 字典的组织方式确实类似JSON，都是键值对结构
person = {
    "name": "Bob",
    "age": 25,
    "address": {
        "street": "Main St",
        "city": "New York"
    }
}
# 字典操作
print(person["name"])  # 访问
person["age"] = 26  # 修改
person["email"] = "<EMAIL>"  # 添加
del person["age"]  # 删除
print(person)
print(person["address"]["city"])

# 3. range用法
# range用于生成数字序列
# 基本用法
for i in range(5):  # 0到4
    print(i)
# 指定起始和结束
for i in range(2, 6):  # 2到5
    print(i)
# 指定步长
for i in range(0, 10, 2):  # 0,2,4,6,8
    print(i)

# 4. Lambda函数
# Lambda函数通常用于简单操作，但也可以写多行
# 单行
add = lambda x, y: x + y
# 多行（使用括号）
complex_op = lambda x: (
    x ** 2 if x > 0 else
    x * 2 if x < 0 else
    0
)
print(complex_op(5))
print(complex_op(-3))

# 5. 装饰器应用
# 装饰器常用于：
# - 日志记录
# - 权限验证
# - 缓存
# - 计时
def log_decorator(func):
    # wrapper是装饰器的核心函数，它包裹并修改原函数的行为
    # *args接收所有位置参数，**kwargs接收所有关键字参数
    def wrapper(*args, **kwargs):
        print(f"Calling {func.__name__}")
        # 调用原函数并保存返回值
        result = func(*args, **kwargs)
        print(f"{func.__name__} finished")
        # 返回原函数的返回值，确保装饰器不会改变函数的行为
        return result
    return wrapper

@log_decorator
def say_hello(name):
    print(f"Hello, {name}!")

say_hello("Alice")

# 6. 异常处理示例
try:
    # 尝试打开不存在的文件
    with open("nonexistent.txt") as f:
        content = f.read()
except FileNotFoundError:
    # Python的except相当于Java的catch
    print("File not found!")
except Exception as e:
    print(f"An error occurred: {e}")
else:
    print("File read successfully")
finally:
    print("This always executes")

# 7. 文件操作
# 展示所有文件模式的区别
# 'w'：只写，会覆盖文件
with open("mode_example.txt", "w") as f:
    f.write("This is w mode\n")
    
# 'r'：只读
try:
    with open("mode_example.txt", "r") as f:
        print(f.read())
except FileNotFoundError:
    print("File not found")

# 'a'：追加
with open("mode_example.txt", "a") as f:
    f.write("This is a mode\n")

# 'w+'：读写，会覆盖文件
with open("mode_example.txt", "w+") as f:
    f.write("This is w+ mode\n")
    # seek(0)将文件指针移动到开头，以便读取刚写入的内容
    f.seek(0)  # 回到文件开头
    print("w+ mode content:")
    print(f.read())

# 'r+'：读写，从文件开头
with open("mode_example.txt", "r+") as f:
    f.write("This is r+ mode\n")
    f.seek(0)
    print("r+ mode content:")
    print(f.read())

# 'a+'：读写，从文件末尾
with open("mode_example.txt", "a+") as f:
    f.write("This is a+ mode\n")
    # 即使使用a+模式，seek(0)仍可读取整个文件
    f.seek(0)
    print("a+ mode content:")
    print(f.read())

# w+和r+的区别：
# w+：打开文件时清空内容，读写指针在文件开头
# r+：打开文件时保留内容，读写指针在文件开头
# 示例：
with open("compare_w_r.txt", "w") as f:  # 先创建文件
    f.write("Original content\n")

# w+模式示例
with open("compare_w_r.txt", "w+") as f:
    print("w+ mode:")
    print("Before write:", f.read())  # 空，因为w+清空了文件
    f.write("New content in w+\n")
    f.seek(0)
    print("After write:", f.read())

# r+模式示例
with open("compare_w_r.txt", "w") as f:  # 重置文件内容
    f.write("Original content\n")

with open("compare_w_r.txt", "r+") as f:
    print("\nr+ mode:")
    print("Before write:", f.read())  # 可以读取原内容
    f.seek(0)
    f.write("New content in r+\n")
    f.seek(0)
    print("After write:", f.read())

# 主要区别：
# 1. w+会清空文件，r+不会
# 2. w+如果文件不存在会创建，r+如果文件不存在会报错
# 3. 两者都可以读写，但初始指针位置不同

# 在文件中间插入内容
def insert_into_file(filename, position, new_content):
    with open(filename, "r+") as f:
        # 读取整个文件内容
        content = f.read()
        
        # 在指定位置插入新内容
        new_content = content[:position] + new_content + content[position:]
        
        # 清空文件并写入新内容
        f.seek(0)
        f.write(new_content)
        f.truncate()  # 确保文件大小正确

# 示例：在文件中间插入内容
with open("insert_example.txt", "w") as f:
    f.write("This is line 1.\nThis is line 3.\n")

print("\nBefore insertion:")
with open("insert_example.txt", "r") as f:
    print(f.read())

insert_into_file("insert_example.txt", 15, "This is line 2.\n")

print("After insertion:")
with open("insert_example.txt", "r") as f:
    print(f.read())

# 6. 异常处理示例
try:
    # 尝试打开不存在的文件
    with open("nonexistent.txt") as f:
        content = f.read()
except FileNotFoundError:
    print("File not found!")
except Exception as e:
    print(f"An error occurred: {e}")
else:
    print("File read successfully")
finally:
    print("This always executes")

# 7. 文件操作
# with open时文件不存在：
# - 'w'模式会自动创建文件
# - 'r'模式会报错
try:
    with open("new_file.txt", "w") as f:
        f.write("This is a new file")
    with open("new_file.txt", "r") as f:
        print(f.read())
except Exception as e:
    print(f"Error: {e}")

# 8. 文件模式
# 'w'：只写，会覆盖文件
# 'r'：只读
# 'a'：追加
# 'w+'：读写，会覆盖文件
# 'r+'：读写，从文件开头
# 'a+'：读写，从文件末尾

# 9. 写文件位置
# 'w'模式会覆盖整个文件
# 'a'模式会在文件末尾追加
with open("example.txt", "w") as f:
    f.write("First line\n")
with open("example.txt", "a") as f:
    f.write("Second line\n")
with open("example.txt", "r") as f:
    print(f.read())