#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试折扣解析修复
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

def test_improved_parsing():
    """测试改进后的解析逻辑"""
    print("🔧 测试改进后的折扣解析")
    print("=" * 50)
    
    # 用户提供的原始数据
    test_data = """盼盼	普货	PANGPANG	1	0.33	6500	5%
生生	普货	LUXIAO	31	2.22	7000	
PANDA	铝膜球	PANDA	360	11	7500	3%
	普货	PANDA	96	5.3	7600	3%
	电器	PANDA	25	1.5	8000	3%"""
    
    print("原始数据：")
    print(test_data)
    print()
    
    # 模拟改进后的解析过程
    lines = test_data.strip().split('\n')
    parsed_data = []
    
    for i, line in enumerate(lines):
        line = line.strip()
        if not line:
            continue
        
        print(f"处理第{i+1}行: '{line}'")
        
        # 分隔数据
        if '\t' in line:
            row = line.split('\t')
        elif ',' in line:
            row = line.split(',')
        else:
            row = line.split()
        
        # 清理数据
        row = [cell.strip() for cell in row]
        print(f"  分隔后: {row} (列数: {len(row)})")

        # 检查是否是客户名为空的行
        if len(row) >= 6 and not row[0]:
            row.insert(0, '')
            print(f"  客户名为空，插入空列: {row}")

        # 确保每行都有7列
        while len(row) < 7:
            row.append('')

        # 只保留前7列
        row = row[:7]
        print(f"  最终格式: {row} (列数: {len(row)})")
        
        # 处理折扣列
        if len(row) > 6 and row[6]:
            discount_str = row[6].strip()
            print(f"  原始折扣: '{discount_str}'")
            
            if discount_str.endswith('%'):
                try:
                    discount_value = float(discount_str[:-1]) / 100
                    row[6] = str(discount_value)
                    print(f"  转换后折扣: {row[6]} ({discount_value*100}%)")
                except ValueError:
                    row[6] = '0'
                    print(f"  转换失败，设为: {row[6]}")
            elif discount_str and discount_str != '0':
                try:
                    float(discount_str)
                    print(f"  保持原值: {row[6]}")
                except ValueError:
                    row[6] = '0'
                    print(f"  无效值，设为: {row[6]}")
        else:
            print(f"  空折扣，保持: '{row[6]}'")
        
        parsed_data.append(row)
        print()
    
    print("最终解析结果：")
    for i, row in enumerate(parsed_data):
        customer = row[0] if row[0] else "空客户"
        discount = row[6] if row[6] else "0"
        try:
            discount_percent = float(discount) * 100
            print(f"  行{i+1}: 客户='{customer}', 折扣={discount} ({discount_percent}%)")
        except ValueError:
            print(f"  行{i+1}: 客户='{customer}', 折扣='{discount}' (无效)")
    
    return parsed_data

def test_with_actual_implementation():
    """使用实际实现测试"""
    print("\n🧪 使用实际实现测试")
    print("=" * 50)
    
    from PyQt5.QtWidgets import QApplication
    app = QApplication([])
    
    try:
        from invoice_generator import InvoiceGenerator, DataPreviewDialog, CustomerPaymentTerms
        
        generator = InvoiceGenerator()
        
        # 模拟剪贴板数据
        test_data = """盼盼	普货	PANGPANG	1	0.33	6500	5%
生生	普货	LUXIAO	31	2.22	7000	
PANDA	铝膜球	PANDA	360	11	7500	3%
	普货	PANDA	96	5.3	7600	3%
	电器	PANDA	25	1.5	8000	3%"""
        
        # 模拟剪贴板读取过程
        lines = test_data.strip().split('\n')
        parsed_data = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            if '\t' in line:
                row = line.split('\t')
            elif ',' in line:
                row = line.split(',')
            else:
                row = line.split()
            
            row = [cell.strip() for cell in row]

            # 检查是否是客户名为空的行
            if len(row) >= 6 and not row[0]:
                row.insert(0, '')

            # 确保每行都有7列
            while len(row) < 7:
                row.append('')

            # 只保留前7列
            row = row[:7]

            # 处理折扣列
            if len(row) > 6 and row[6]:
                discount_str = row[6].strip()
                if discount_str.endswith('%'):
                    try:
                        discount_value = float(discount_str[:-1]) / 100
                        row[6] = str(discount_value)
                    except ValueError:
                        row[6] = '0'
                elif discount_str and discount_str != '0':
                    try:
                        float(discount_str)
                    except ValueError:
                        row[6] = '0'
            
            parsed_data.append(row)
        
        generator.clipboard_data = parsed_data
        
        # 测试数据处理
        clipboard_rows = generator._load_clipboard_data()
        filled_rows = generator._forward_fill_column(clipboard_rows.copy(), 0)
        grouped_data = generator._group_by_column(filled_rows, 0)
        
        print("分组和前向填充结果：")
        for customer, rows in grouped_data.items():
            print(f"客户: '{customer}'")
            for j, row in enumerate(rows):
                discount_raw = row[6] if len(row) > 6 else "0"
                try:
                    discount_num = float(discount_raw)
                    print(f"  行{j+1}: 折扣={discount_raw} ({discount_num*100}%)")
                except ValueError:
                    print(f"  行{j+1}: 折扣='{discount_raw}' (无效)")
        
        # 测试DataPreviewDialog
        print("\n测试DataPreviewDialog:")
        payment_terms = CustomerPaymentTerms()
        dialog = DataPreviewDialog(grouped_data, payment_terms)
        
        print("DataPreviewDialog中的客户折扣:")
        for customer, rows in grouped_data.items():
            for data_row in rows:
                discount_col_index = dialog._get_discount_column_index(data_row)
                if discount_col_index < len(data_row):
                    discount_raw = data_row[discount_col_index]
                    try:
                        discount = float(discount_raw)
                        print(f"  {customer}: 折扣={discount} ({discount*100}%)")
                        break
                    except (ValueError, TypeError):
                        print(f"  {customer}: 折扣转换失败='{discount_raw}'")
                        break
        
        print("\n✅ 测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_improved_parsing()
    test_with_actual_implementation()
