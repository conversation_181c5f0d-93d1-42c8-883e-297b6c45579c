#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试真实的剪贴板数据格式
"""

def test_real_clipboard_format():
    """测试真实的剪贴板数据格式"""
    print("🔍 测试真实剪贴板数据格式")
    print("=" * 50)
    
    # 根据用户描述，重新构造真实的数据格式
    # 第4行和第5行开头应该是空的（制表符开头）
    test_data = "盼盼\t普货\tPANGPANG\t1\t0.33\t6500\t5%\n生生\t普货\tLUXIAO\t31\t2.22\t7000\t\nPANDA\t铝膜球\tPANDA\t360\t11\t7500\t3%\n\t普货\tPANDA\t96\t5.3\t7600\t3%\n\t电器\tPANDA\t25\t1.5\t8000\t3%"
    
    print("真实数据格式（用\\t表示制表符）：")
    for i, line in enumerate(test_data.split('\n')):
        print(f"行{i+1}: {repr(line)}")
    print()
    
    # 解析过程
    lines = test_data.strip().split('\n')
    parsed_data = []
    
    for i, line in enumerate(lines):
        print(f"处理第{i+1}行: {repr(line)}")
        
        # 分隔数据
        row = line.split('\t')
        print(f"  制表符分隔: {row}")
        
        # 清理数据
        row = [cell.strip() for cell in row]
        print(f"  清理后: {row} (列数: {len(row)})")
        
        # 检查是否是客户名为空的行
        if len(row) >= 6 and not row[0]:
            print(f"  检测到客户名为空，原始行: {row}")
            # 这种情况下，数据实际上是：空客户、货品、唛头、件数、立方、单价、折扣
            # 不需要插入额外的空列，因为第一列已经是空的
        
        # 确保每行都有7列
        while len(row) < 7:
            row.append('')
        
        # 只保留前7列
        row = row[:7]
        print(f"  最终格式: {row}")
        
        # 处理折扣列
        if len(row) > 6 and row[6]:
            discount_str = row[6].strip()
            print(f"  原始折扣: '{discount_str}'")
            
            if discount_str.endswith('%'):
                try:
                    discount_value = float(discount_str[:-1]) / 100
                    row[6] = str(discount_value)
                    print(f"  转换后折扣: {row[6]} ({discount_value*100}%)")
                except ValueError:
                    row[6] = '0'
                    print(f"  转换失败，设为: {row[6]}")
        else:
            print(f"  空折扣: '{row[6]}'")
        
        parsed_data.append(row)
        print()
    
    print("最终解析结果：")
    for i, row in enumerate(parsed_data):
        customer = row[0] if row[0] else "[空客户]"
        discount = row[6] if row[6] else "0"
        try:
            discount_percent = float(discount) * 100
            print(f"  行{i+1}: 客户='{customer}', 折扣={discount} ({discount_percent}%)")
        except ValueError:
            print(f"  行{i+1}: 客户='{customer}', 折扣='{discount}' (无效)")
    
    # 测试前向填充
    print("\n测试前向填充：")
    filled_data = []
    for row in parsed_data:
        new_row = row.copy()
        if not new_row[0] and filled_data:  # 如果客户名为空且不是第一行
            new_row[0] = filled_data[-1][0]  # 使用上一行的客户名
        filled_data.append(new_row)
    
    for i, row in enumerate(filled_data):
        customer = row[0] if row[0] else "[空客户]"
        discount = row[6] if row[6] else "0"
        try:
            discount_percent = float(discount) * 100
            print(f"  行{i+1}: 客户='{customer}', 折扣={discount} ({discount_percent}%)")
        except ValueError:
            print(f"  行{i+1}: 客户='{customer}', 折扣='{discount}' (无效)")
    
    return filled_data

if __name__ == "__main__":
    test_real_clipboard_format()
