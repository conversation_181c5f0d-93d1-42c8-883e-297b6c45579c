#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试剪贴板功能的bug修复
验证数据加载过程中不会出现"list index out of range"错误
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

def test_clipboard_data_loading():
    """测试剪贴板数据加载功能"""
    print("🔧 测试剪贴板数据加载bug修复")
    print("=" * 50)
    
    app = QApplication([])
    
    try:
        from invoice_generator import InvoiceGenerator
        
        # 创建发票生成器实例
        generator = InvoiceGenerator()
        
        # 模拟剪贴板数据（7列格式）
        test_clipboard_data = """客户A	货品1	唛头1	10	1.5	100	0.05
客户A	货品2	唛头2	5	0.8	200	0.05
客户B	货品3	唛头3	8	2.0	150	0"""
        
        # 解析数据
        lines = test_clipboard_data.strip().split('\n')
        parsed_data = []
        for line in lines:
            row = line.split('\t')
            row = [cell.strip() for cell in row]
            parsed_data.append(row)
        
        print(f"测试数据：{len(parsed_data)} 行")
        for i, row in enumerate(parsed_data):
            print(f"  行{i+1}: {row} (列数: {len(row)})")
        
        # 设置剪贴板数据
        generator.clipboard_data = parsed_data
        
        # 测试数据处理方法
        print("\n测试数据处理方法...")
        
        # 测试列索引获取
        test_row = parsed_data[0]
        quantity_idx, volume_idx, unit_price_idx, discount_idx = generator._get_column_indices(test_row)
        print(f"列索引映射: 件数={quantity_idx}, 立方={volume_idx}, 单价={unit_price_idx}, 折扣={discount_idx}")
        
        # 测试数据加载
        clipboard_rows = generator._load_clipboard_data()
        print(f"加载的数据行数: {len(clipboard_rows)}")
        
        # 测试前向填充
        filled_rows = generator._forward_fill_column(clipboard_rows.copy(), 0)
        print(f"前向填充后行数: {len(filled_rows)}")
        
        # 测试分组
        grouped_data = generator._group_by_column(filled_rows, 0)
        print(f"分组结果: {len(grouped_data)} 个客户")
        for customer, rows in grouped_data.items():
            print(f"  {customer}: {len(rows)} 条记录")
        
        # 测试DataPreviewDialog的数据处理
        print("\n测试DataPreviewDialog数据处理...")
        from invoice_generator import DataPreviewDialog, CustomerPaymentTerms
        
        payment_terms = CustomerPaymentTerms()
        dialog = DataPreviewDialog(grouped_data, payment_terms)
        
        # 测试折扣列索引获取
        test_row = list(grouped_data.values())[0][0]  # 获取第一个客户的第一行数据
        discount_col_idx = dialog._get_discount_column_index(test_row)
        print(f"折扣列索引: {discount_col_idx}")
        
        # 验证折扣值访问
        if discount_col_idx >= 0 and discount_col_idx < len(test_row):
            discount_value = test_row[discount_col_idx]
            print(f"折扣值: {discount_value}")
        else:
            print("折扣列索引无效")
        
        print("\n✅ 所有测试通过！bug已修复")
        print("现在可以安全地使用剪贴板功能进行数据加载")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = test_clipboard_data_loading()
    if success:
        print("\n🎉 Bug修复验证成功！")
    else:
        print("\n💥 Bug修复验证失败！")
