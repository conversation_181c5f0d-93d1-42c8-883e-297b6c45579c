# This is a demo file to learn about Python data types and basic syntax

# Numeric Types
# Integer
age = 30
print(f"Age: {age}, Type: {type(age)}")

# Float
price = 99.99
print(f"Price: {price}, Type: {type(price)}")

# Complex
complex_num = 2 + 3j
print(f"Complex Number: {complex_num}, Type: {type(complex_num)}")

# String Type
name = "Alice"
print(f"Name: {name}, Type: {type(name)}")
# String的更多操作
print(name[0])
print(name[1:3])
print(name.upper())
print(name.lower())
print(name.replace("A", "B"))

# Boolean Type
is_student = True
print(f"Is Student: {is_student}, Type: {type(is_student)}")

# List Type
fruits = ["apple", "banana", "cherry"]
print(f"Fruits: {fruits}, Type: {type(fruits)}")
fruits.append("orange")
print(f"Fruits after append: {fruits}")

# Tuple Type
coordinates = (10, 20)
print(f"Coordinates: {coordinates}, Type: {type(coordinates)}")

# Dictionary Type
person = {"name": "<PERSON>", "age": 25, "city": "New York"}
print(f"Person: {person}, Type: {type(person)}")
print(f"Person's name: {person['name']}")
print(f"Person's age: {person['age']}")

# Set Type
unique_numbers = {1, 2, 3, 2}
print(f"Unique Numbers: {unique_numbers}, Type: {type(unique_numbers)}")

# Basic Syntax
# If statement
if age >= 18:
    print("You are an adult.")
else:
    print("You are a minor.")

# For loop
for fruit in fruits:
    print(fruit)

# While loop
count = 0
while count < 3:
    print(f"Count: {count}")
    count += 1

# Function
def greet(name):
    print(f"Hello, {name}!")

greet("Charlie")

# List Comprehension
# 列表推导式是一种简洁的创建列表的方法
squares = [x**2 for x in range(10)]
print(f"Squares: {squares}, Type: {type(squares)}")

# Generator Expression
# 生成器表达式类似于列表推导式，但它们创建一个生成器对象而不是列表
# 生成器对象一次生成一个值，这使得它们在处理大型数据集时非常高效
even_numbers = (x for x in range(20) if x % 2 == 0)
print(f"Even Numbers: {even_numbers}, Type: {type(even_numbers)}")
for num in even_numbers:
    print(num)

# Lambda Function
# Lambda 函数是匿名函数，它们可以快速定义简单的函数
add = lambda x, y: x + y
print(f"Sum of 5 and 3: {add(5, 3)}")

# Decorator
# 装饰器是一种修改函数或类行为的方法
def my_decorator(func):
    def wrapper():
        print("Something is happening before the function is called.")
        func()
        print("Something is happening after the function is called.")
    return wrapper

@my_decorator
def say_hello():
    print("Hello!")

say_hello()

# Exception Handling
# 异常处理允许程序在发生错误时继续运行
try:
    result = 10 / 0
except ZeroDivisionError:
    print("Cannot divide by zero!")

# File Operations
# Python 提供了多种处理文件的方法
with open("sample.txt", "w") as f:
    f.write("This is a sample file.")

with open("sample.txt", "r") as f:
    content = f.read()
    print(f"File Content: {content}")
