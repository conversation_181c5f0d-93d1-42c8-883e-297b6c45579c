import sys
from PyQt5.QtWidgets import (QApplication, QWidget, QLabel,
                            QLineEdit, QPushButton, QFileDialog, QMessageBox,
                            QGridLayout, QDialog, QTableWidget, QTableWidgetItem,
                            QVBoxLayout, QHBoxLayout, QCheckBox, QDateEdit,
                            QRadioButton, QButtonGroup, QSpinBox, QGroupBox)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtWidgets import QDialog, QApplication
import openpyxl
from openpyxl import Workbook
from docxtpl import DocxTemplate
import os
import subprocess
from datetime import datetime
import win32com.client
import pythoncom


class CustomerPaymentTerms:
    """客户账期管理类"""
    def __init__(self, file_path="customer_payment_terms.xlsx"):
        self.file_path = file_path
        self.ensure_file_exists()

    def ensure_file_exists(self):
        """确保账期文件存在，如果不存在则创建"""
        if not os.path.exists(self.file_path):
            self.create_default_file()

    def create_default_file(self):
        """创建默认的账期管理文件"""
        wb = Workbook()
        ws = wb.active
        ws.title = "CustomerPaymentTerms"

        # 设置表头
        headers = ["客户名称", "账期天数", "折扣政策提示"]
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)

        wb.save(self.file_path)
        wb.close()

    def get_customer_terms(self, customer_name):
        """获取客户的账期设置"""
        try:
            wb = openpyxl.load_workbook(self.file_path, data_only=True)
            ws = wb.active

            # 查找客户
            for row in ws.iter_rows(min_row=2, values_only=True):
                if row[0] == customer_name:
                    payment_days = row[1] if row[1] is not None else 7
                    show_policy = row[2] if row[2] is not None else "是"
                    wb.close()
                    return payment_days, show_policy

            wb.close()
            # 如果没找到客户，返回默认值
            return 7, "是"

        except Exception as e:
            print(f"读取账期文件出错: {e}")
            return 7, "是"

    def update_customer_terms(self, customer_data):
        """更新客户账期设置
        customer_data: dict, key为客户名称，value为(payment_days, show_policy)的元组
        """
        try:
            wb = openpyxl.load_workbook(self.file_path)
            ws = wb.active

            # 获取现有数据
            existing_customers = {}
            rows_to_update = {}

            for row_num, row in enumerate(ws.iter_rows(min_row=2, values_only=True), start=2):
                if row[0] is not None:
                    existing_customers[row[0]] = row_num

            # 更新现有客户或添加新客户
            for customer_name, (payment_days, show_policy) in customer_data.items():
                if customer_name in existing_customers:
                    # 更新现有客户
                    row_num = existing_customers[customer_name]
                    ws.cell(row=row_num, column=2, value=payment_days)
                    ws.cell(row=row_num, column=3, value=show_policy)
                else:
                    # 添加新客户
                    new_row = ws.max_row + 1
                    ws.cell(row=new_row, column=1, value=customer_name)
                    ws.cell(row=new_row, column=2, value=payment_days)
                    ws.cell(row=new_row, column=3, value=show_policy)

            wb.save(self.file_path)
            wb.close()

        except Exception as e:
            print(f"更新账期文件出错: {e}")


class DataPreviewDialog(QDialog):
    """数据预览和设置对话框"""
    def __init__(self, grouped_data, payment_terms, parent=None):
        super().__init__(parent)
        self.grouped_data = grouped_data
        self.payment_terms = payment_terms
        self.customer_settings = {}  # 存储客户设置

        self.setWindowTitle("数据预览和设置")
        self.setModal(True)
        self.resize(800, 600)

        self.init_ui()
        self.load_customer_data()

    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout()

        # 说明标签
        info_label = QLabel("请检查并设置客户账期信息（仅对有折扣的客户有效）：")
        layout.addWidget(info_label)

        # 创建表格
        self.table = QTableWidget()
        self.table.setColumnCount(4)
        self.table.setHorizontalHeaderLabels(["客户", "折扣", "账期(天)", "折扣政策提示"])

        # 设置表格列宽
        self.table.setColumnWidth(0, 200)
        self.table.setColumnWidth(1, 100)
        self.table.setColumnWidth(2, 100)
        self.table.setColumnWidth(3, 120)

        # 设置表格为只读，然后通过自定义逻辑控制特定单元格的编辑
        self.table.setEditTriggers(QTableWidget.NoEditTriggers)

        # 连接单元格双击事件，用于控制编辑
        self.table.cellDoubleClicked.connect(self.on_cell_double_clicked)

        layout.addWidget(self.table)

        # 按钮
        button_layout = QHBoxLayout()

        self.confirm_btn = QPushButton("确认并生成发票")
        self.confirm_btn.clicked.connect(self.accept)

        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)

        button_layout.addStretch()
        button_layout.addWidget(self.cancel_btn)
        button_layout.addWidget(self.confirm_btn)

        layout.addLayout(button_layout)
        self.setLayout(layout)

    def load_customer_data(self):
        """加载客户数据到表格"""
        customers = list(self.grouped_data.keys())
        self.table.setRowCount(len(customers))

        for row, customer_name in enumerate(customers):
            # 计算客户的折扣（假设同一客户的折扣相同）
            group = self.grouped_data[customer_name]
            discount = 0
            for data_row in group:
                if ExcelData.is_not_none(data_row[8]):  # 折扣在第9列，索引为8
                    discount = data_row[8]
                    break

            # 获取账期设置
            payment_days, show_policy = self.payment_terms.get_customer_terms(customer_name)

            # 客户名称（不可编辑）
            customer_item = QTableWidgetItem(customer_name)
            self.table.setItem(row, 0, customer_item)

            # 折扣（不可编辑）
            discount_text = f"{int(discount*100)}%" if discount != 0 else "无折扣"
            discount_item = QTableWidgetItem(discount_text)
            self.table.setItem(row, 1, discount_item)

            # 账期（可编辑，但仅当有折扣时有效）
            payment_spinbox = QSpinBox()
            payment_spinbox.setRange(1, 365)
            payment_spinbox.setValue(payment_days)
            payment_spinbox.setEnabled(discount != 0)  # 仅当有折扣时启用
            self.table.setCellWidget(row, 2, payment_spinbox)

            # 折扣政策提示勾选框（仅当有折扣时有效）
            policy_checkbox = QCheckBox()
            if discount == 0:
                # 无折扣时默认设为"否"且禁用
                policy_checkbox.setChecked(False)
                policy_checkbox.setEnabled(False)
            else:
                # 有折扣时根据设置决定
                policy_checkbox.setChecked(show_policy == "是")
                policy_checkbox.setEnabled(True)

            self.table.setCellWidget(row, 3, policy_checkbox)

            # 存储客户设置
            self.customer_settings[customer_name] = {
                'discount': discount,
                'payment_spinbox': payment_spinbox,
                'policy_checkbox': policy_checkbox
            }

    def get_customer_settings(self):
        """获取用户设置的客户信息"""
        settings = {}
        for customer_name, widgets in self.customer_settings.items():
            payment_days = widgets['payment_spinbox'].value()
            show_policy = "是" if widgets['policy_checkbox'].isChecked() else "否"
            discount = widgets['discount']

            settings[customer_name] = {
                'payment_days': payment_days,
                'show_policy': show_policy,
                'discount': discount
            }

        return settings

    def on_cell_double_clicked(self, row, column):
        """处理单元格双击事件，控制哪些列可以编辑"""
        # 只允许编辑第2列（账期）和第3列（折扣政策提示）
        if column not in [2, 3]:
            return

        # 检查该行客户是否有折扣
        customer_item = self.table.item(row, 0)
        if customer_item is None:
            return

        customer_name = customer_item.text()
        if customer_name in self.customer_settings:
            discount = self.customer_settings[customer_name]['discount']
            # 如果没有折扣，不允许编辑账期和政策提示
            if discount == 0:
                return

        # 临时启用编辑功能
        self.table.setEditTriggers(QTableWidget.AllEditTriggers)

        # 开始编辑
        target_item = self.table.item(row, column)
        if target_item is not None:
            self.table.editItem(target_item)

        # 连接编辑完成信号，编辑完成后重新禁用编辑
        self.table.itemChanged.connect(self.on_item_changed)

    def on_item_changed(self, item):
        """处理表格项更改事件"""
        # 编辑完成后重新设置为只读
        self.table.setEditTriggers(QTableWidget.NoEditTriggers)
        # 断开信号连接，避免重复触发
        self.table.itemChanged.disconnect(self.on_item_changed)


class ExcelData:
    """使用openpyxl处理Excel数据的高效辅助类"""
    def __init__(self, file_path):
        self.workbook = openpyxl.load_workbook(file_path, read_only=True, data_only=True)
        self.worksheet = self.workbook.active

    def get_rows_range(self, start_row, end_row):
        """直接获取指定行范围的数据，不预加载全部数据（更高效）"""
        rows = []
        for row in self.worksheet.iter_rows(min_row=start_row, max_row=end_row, values_only=True):
            rows.append(list(row))
        return rows

    def forward_fill_column(self, rows, col_index):
        """前向填充指定列的空值"""
        last_value = None
        for row in rows:
            if len(row) > col_index:
                if row[col_index] is not None:
                    last_value = row[col_index]
                elif last_value is not None:
                    row[col_index] = last_value
        return rows

    def group_by_column(self, rows, col_index):
        """按指定列分组"""
        groups = {}
        for row in rows:
            if len(row) > col_index:
                key = row[col_index]
                if key is not None:  # 跳过空值
                    if key not in groups:
                        groups[key] = []
                    groups[key].append(row)
        return groups

    @staticmethod
    def is_not_none(value):
        """检查值是否不为None（替代pd.notna）"""
        return value is not None

    def close(self):
        """关闭工作簿释放资源"""
        if hasattr(self, 'workbook'):
            self.workbook.close()


class InvoiceGenerator(QWidget):
    # 显式声明layout属性
    layout: QGridLayout

    def __init__(self):
        super().__init__()
        self.setWindowTitle("发票批量生成器")

        # 文件选择
        self.excel_file = None
        self.template_file = os.path.join(os.path.dirname(__file__), "invoice_template.docx")

        # COM对象复用 - 添加Word应用实例变量
        self.word_app = None
        self.com_initialized = False

        # 模板预加载缓存
        self.cached_template = None
        self.cached_template_path = None

        # 账期管理
        self.payment_terms = CustomerPaymentTerms()

        # 数据预览相关
        self.customer_data = None  # 存储加载的客户数据
        self.grouped_data = None   # 存储分组后的数据
        self.clipboard_data = None # 存储剪贴板数据

        # 创建默认输出文件夹
        self.default_output_dir = os.path.join(os.getcwd(), "generated_invoices")
        if not os.path.exists(self.default_output_dir):
            os.makedirs(self.default_output_dir)

        # 布局
        self.layout = QGridLayout()
        self.setLayout(self.layout)

        # 状态标签
        self.status_label = QLabel("")

        # GUI组件
        self.create_widgets()

    def init_word_app(self):
        """初始化Word应用程序实例"""
        if not self.com_initialized:
            pythoncom.CoInitialize()
            self.com_initialized = True

        if self.word_app is None:
            try:
                # 尝试获取已存在的Word实例
                self.word_app = win32com.client.GetObject(Class="Word.Application")
            except:
                # 创建新的Word实例
                self.word_app = win32com.client.Dispatch("Word.Application")

            self.word_app.Visible = False
            self.word_app.DisplayAlerts = False

    def cleanup_word_app(self):
        """清理Word应用程序实例"""
        if self.word_app is not None:
            try:
                self.word_app.Quit()
            except:
                pass
            finally:
                self.word_app = None

        if self.com_initialized:
            pythoncom.CoUninitialize()
            self.com_initialized = False

    def load_template(self, template_path):
        """预加载模板，避免重复加载"""
        if self.cached_template is None or self.cached_template_path != template_path:
            self.cached_template = DocxTemplate(template_path)
            self.cached_template_path = template_path
        return self.cached_template

    def get_template(self):
        """获取缓存的模板实例（可重复使用）"""
        if self.cached_template is None:
            raise ValueError("模板未加载，请先调用load_template")
        # DocxTemplate实例可以重复使用，无需创建副本
        return self.cached_template

    def closeEvent(self, event):
        """窗口关闭时清理资源"""
        self.cleanup_word_app()
        event.accept()

    def create_widgets(self):
        # 模板文件选择
        self.layout.addWidget(QLabel("Word模板:"), 0, 0)
        self.template_path = QLineEdit(self.template_file)
        self.template_path.setReadOnly(True)
        self.layout.addWidget(self.template_path, 0, 1)
        self.template_btn = QPushButton("选择文件")
        self.template_btn.clicked.connect(self.select_template)
        self.layout.addWidget(self.template_btn, 0, 2)

        # 生成设置
        self.layout.addWidget(QLabel("柜号:"), 1, 0)
        self.container_num = QLineEdit()
        self.layout.addWidget(self.container_num, 1, 1)

        self.layout.addWidget(QLabel("发票号:"), 2, 0)
        self.invoice_num = QLineEdit()
        self.layout.addWidget(self.invoice_num, 2, 1)

        # 数据源选择组
        data_source_group = QGroupBox("数据源选择")
        data_source_layout = QVBoxLayout()

        # 方式1：Excel文件
        self.file_mode_radio = QRadioButton("从Excel文件读取数据")
        self.file_mode_radio.setChecked(True)  # 默认选择
        data_source_layout.addWidget(self.file_mode_radio)

        # Excel文件选择和行号设置
        excel_group = QGroupBox("Excel文件设置")
        excel_layout = QVBoxLayout()

        # Excel文件选择
        file_select_layout = QHBoxLayout()
        file_select_layout.addWidget(QLabel("Excel文件:"))
        self.excel_path = QLineEdit()
        self.excel_path.setReadOnly(True)
        file_select_layout.addWidget(self.excel_path)
        self.excel_btn = QPushButton("选择文件")
        self.excel_btn.clicked.connect(self.select_excel)
        file_select_layout.addWidget(self.excel_btn)
        excel_layout.addLayout(file_select_layout)

        # 行号范围设置
        file_range_layout = QHBoxLayout()
        file_range_layout.addWidget(QLabel("起始行号:"))
        self.start_row = QLineEdit()
        file_range_layout.addWidget(self.start_row)
        file_range_layout.addWidget(QLabel("结束行号:"))
        self.end_row = QLineEdit()
        file_range_layout.addWidget(self.end_row)
        excel_layout.addLayout(file_range_layout)

        excel_group.setLayout(excel_layout)
        data_source_layout.addWidget(excel_group)

        # 方式2：剪贴板数据
        self.clipboard_mode_radio = QRadioButton("从剪贴板读取数据")
        data_source_layout.addWidget(self.clipboard_mode_radio)

        clipboard_layout = QVBoxLayout()

        # 剪贴板使用说明
        clipboard_instruction = QLabel(
            "请复制包含以下7列数据的区域（不包含表头）：\n"
            "客户 | 货品 | 唛头 | 件数 | 立方 | 单价 | 折扣\n"
            "注意：只复制数据行，不要包含表头"
        )
        clipboard_instruction.setStyleSheet("color: #666; font-size: 11px; padding: 5px; background-color: #f5f5f5; border: 1px solid #ddd;")
        clipboard_instruction.setWordWrap(True)
        clipboard_layout.addWidget(clipboard_instruction)

        clipboard_btn_layout = QHBoxLayout()
        self.read_clipboard_btn = QPushButton("读取剪贴板数据")
        self.read_clipboard_btn.clicked.connect(self.read_clipboard_data)
        self.read_clipboard_btn.setEnabled(False)  # 默认禁用
        clipboard_btn_layout.addWidget(self.read_clipboard_btn)

        self.clipboard_status_label = QLabel("请先复制数据区域")
        self.clipboard_status_label.setStyleSheet("color: gray;")
        clipboard_btn_layout.addWidget(self.clipboard_status_label)
        clipboard_layout.addLayout(clipboard_btn_layout)

        data_source_layout.addLayout(clipboard_layout)

        # 连接单选按钮信号
        self.file_mode_radio.toggled.connect(self.on_data_source_changed)
        self.clipboard_mode_radio.toggled.connect(self.on_data_source_changed)

        data_source_group.setLayout(data_source_layout)
        self.layout.addWidget(data_source_group, 4, 0, 1, 3)

        # 提示信息
        self.layout.addWidget(QLabel("提示：Excel模式下如果只需要生成一条数据对应的发票，两个输入框应该填同一个行号。\n剪贴板模式下请确保复制的数据包含所有必要的列信息。"),
                            5, 0, 1, 3)

        # 日期选择
        self.layout.addWidget(QLabel("发票日期:"), 7, 0)

        # 日期选择按钮组
        self.date_group = QButtonGroup()
        self.current_date_radio = QRadioButton("使用当前日期")
        self.custom_date_radio = QRadioButton("自定义日期")
        self.current_date_radio.setChecked(True)  # 默认选择当前日期

        self.date_group.addButton(self.current_date_radio)
        self.date_group.addButton(self.custom_date_radio)

        # 自定义日期选择器
        self.custom_date_edit = QDateEdit()
        self.custom_date_edit.setDate(QDate.currentDate())
        self.custom_date_edit.setEnabled(False)  # 默认禁用

        # 连接信号
        self.current_date_radio.toggled.connect(self.on_date_option_changed)
        self.custom_date_radio.toggled.connect(self.on_date_option_changed)

        # 日期控件布局
        date_layout = QHBoxLayout()
        date_layout.addWidget(self.current_date_radio)
        date_layout.addWidget(self.custom_date_radio)
        date_layout.addWidget(self.custom_date_edit)

        date_widget = QWidget()
        date_widget.setLayout(date_layout)
        self.layout.addWidget(date_widget, 7, 1, 1, 2)

        # 运输编号（原输出文件夹）
        self.layout.addWidget(QLabel("运输编号:"), 8, 0)
        self.output_folder = QLineEdit()
        self.layout.addWidget(self.output_folder, 8, 1)

        # 加载数据按钮（原生成按钮）
        self.load_data_btn = QPushButton("加载数据")
        self.load_data_btn.clicked.connect(self.load_data)
        self.layout.addWidget(self.load_data_btn, 9, 1)

        # 状态标签
        self.status_label.setStyleSheet("color: blue;")
        self.layout.addWidget(self.status_label, 10, 0, 1, 3)

    def on_data_source_changed(self):
        """数据源选择改变时的处理"""
        if self.file_mode_radio.isChecked():
            # 启用Excel文件相关控件
            self.start_row.setEnabled(True)
            self.end_row.setEnabled(True)
            self.read_clipboard_btn.setEnabled(False)
            self.clipboard_status_label.setText("请先复制包含完整数据的表格区域")
            self.clipboard_status_label.setStyleSheet("color: gray;")
        else:
            # 启用剪贴板相关控件
            self.start_row.setEnabled(False)
            self.end_row.setEnabled(False)
            self.read_clipboard_btn.setEnabled(True)
            self.clipboard_status_label.setText("点击按钮读取剪贴板数据")
            self.clipboard_status_label.setStyleSheet("color: blue;")

    def read_clipboard_data(self):
        """读取剪贴板数据"""
        try:
            clipboard = QApplication.clipboard()
            clipboard_text = clipboard.text()

            if not clipboard_text.strip():
                QMessageBox.warning(self, "错误", "剪贴板中没有文本数据")
                return

            # 解析剪贴板数据（不包含表头，只有数据行）
            lines = clipboard_text.strip().split('\n')
            if len(lines) < 1:  # 至少需要一行数据
                QMessageBox.warning(self, "错误", "剪贴板中没有有效数据")
                return

            # 解析为二维数组
            parsed_data = []
            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # 尝试制表符分隔，如果没有则尝试逗号分隔
                if '\t' in line:
                    row = line.split('\t')
                elif ',' in line:
                    row = line.split(',')
                else:
                    # 空格分隔作为最后选择
                    row = line.split()

                # 清理每个单元格的数据
                row = [cell.strip() for cell in row]
                parsed_data.append(row)

            # 验证数据格式（需要7列：客户、货品、唛头、件数、立方、单价、折扣）
            if len(parsed_data) == 0:
                QMessageBox.warning(self, "错误", "没有解析到有效数据")
                return

            if len(parsed_data[0]) < 7:
                QMessageBox.warning(self, "错误",
                    f"数据列数不足，需要7列（客户、货品、唛头、件数、立方、单价、折扣），当前只有{len(parsed_data[0])}列")
                return

            # 存储剪贴板数据
            self.clipboard_data = parsed_data

            # 更新状态
            self.clipboard_status_label.setText(f"已读取 {len(parsed_data)} 行数据")
            self.clipboard_status_label.setStyleSheet("color: green;")

            QMessageBox.information(self, "成功", f"成功读取剪贴板数据：{len(parsed_data)} 行记录")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"读取剪贴板数据时出错：{str(e)}")
            self.clipboard_status_label.setText("读取失败，请重新复制数据")
            self.clipboard_status_label.setStyleSheet("color: red;")

    def on_date_option_changed(self):
        """日期选择选项改变时的处理"""
        self.custom_date_edit.setEnabled(self.custom_date_radio.isChecked())

    def get_selected_date(self):
        """获取用户选择的日期"""
        if self.current_date_radio.isChecked():
            return datetime.now()
        else:
            selected_date = self.custom_date_edit.date().toPyDate()
            return datetime.combine(selected_date, datetime.min.time())

    def select_excel(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "选择Excel文件", "",
                                                 "Excel files (*.xlsx *.xls)")
        if file_path:
            self.excel_path.setText(file_path)
            self.excel_file = file_path

    def select_template(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "选择Word模板", "",
                                                 "Word files (*.docx)")
        if file_path:
            self.template_path.setText(file_path)

    def load_data(self):
        """加载数据并显示预览对话框"""
        try:
            # 验证基本输入
            if not self.container_num.text() or not self.invoice_num.text():
                QMessageBox.warning(self, "错误", "请输入柜号和发票号")
                return

            # 根据数据源模式进行不同的验证和处理
            if self.file_mode_radio.isChecked():
                # Excel文件模式
                if not self.excel_file:
                    QMessageBox.warning(self, "错误", "请先选择Excel文件")
                    return

                if not self.start_row.text() or not self.end_row.text():
                    QMessageBox.warning(self, "错误", "请输入起始行号和结束行号")
                    return

                self.update_status("正在读取Excel数据...")
                selected_rows = self._load_excel_data()

            else:
                # 剪贴板模式
                if self.clipboard_data is None:
                    QMessageBox.warning(self, "错误", "请先读取剪贴板数据")
                    return

                self.update_status("正在处理剪贴板数据...")
                selected_rows = self._load_clipboard_data()

            if not selected_rows:
                QMessageBox.warning(self, "错误", "没有读取到有效数据")
                return

            # 根据数据源确定客户列位置
            if self.file_mode_radio.isChecked():
                # Excel数据：客户在第3列（索引2）
                customer_col_index = 2
            else:
                # 剪贴板数据：客户在第1列（索引0）
                customer_col_index = 0

            # 处理合并单元格：前向填充客户名称
            selected_rows = self._forward_fill_column(selected_rows, customer_col_index)

            # 按客户分组
            grouped = self._group_by_column(selected_rows, customer_col_index)

            # 存储数据供后续使用
            self.customer_data = selected_rows
            self.grouped_data = grouped

            # 显示数据预览对话框
            self.show_data_preview_dialog()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载数据时出错: {str(e)}")
            self.update_status(f"加载数据失败: {str(e)}")

    def _load_excel_data(self):
        """从Excel文件加载数据"""
        excel_data = ExcelData(self.excel_file)
        try:
            # 解析行号范围
            start_row = int(self.start_row.text())
            end_row = int(self.end_row.text())
            selected_rows = excel_data.get_rows_range(start_row, end_row)
            return selected_rows
        finally:
            # 确保Excel文件资源被正确释放
            excel_data.close()

    def _load_clipboard_data(self):
        """从剪贴板数据加载数据"""
        # 剪贴板数据已经是解析好的二维数组，不包含表头，直接返回
        return self.clipboard_data

    def _forward_fill_column(self, rows, col_index):
        """前向填充指定列的空值"""
        last_value = None
        for row in rows:
            if col_index < len(row):
                if row[col_index] and str(row[col_index]).strip():
                    last_value = row[col_index]
                elif last_value is not None:
                    row[col_index] = last_value
        return rows

    def _group_by_column(self, rows, col_index):
        """按指定列分组数据"""
        grouped = {}
        for row in rows:
            if col_index < len(row) and row[col_index]:
                key = str(row[col_index]).strip()
                if key not in grouped:
                    grouped[key] = []
                grouped[key].append(row)
        return grouped

    def show_data_preview_dialog(self):
        """显示数据预览对话框"""
        dialog = DataPreviewDialog(self.grouped_data, self.payment_terms, self)

        if dialog.exec_() == QDialog.Accepted:
            # 用户确认，获取设置并保存
            customer_settings = dialog.get_customer_settings()

            # 更新账期文件
            payment_terms_data = {}
            for customer_name, settings in customer_settings.items():
                payment_terms_data[customer_name] = (
                    settings['payment_days'],
                    settings['show_policy']
                )

            self.payment_terms.update_customer_terms(payment_terms_data)

            # 存储客户设置供发票生成使用
            self.final_customer_settings = customer_settings

            # 开始生成发票
            self.generate_invoices_with_settings()
        else:
            # 用户取消
            self.update_status("用户取消了操作")

    def generate_discount_policy_text(self, payment_days, discount_percent):
        """生成折扣政策文本"""
        return f"遵循公司最新优惠政策，收到货后\n\n{payment_days}天内付款此单享有{discount_percent}%的折扣。"

    def generate_invoices_with_settings(self):
        """使用设置生成发票"""
        self.error_log = []  # 初始化错误日志
        try:
            # 验证数据是否已加载
            if not self.grouped_data:
                QMessageBox.warning(self, "错误", "没有加载的数据，请先点击加载数据")
                return

            # 禁用加载数据按钮
            self.load_data_btn.setEnabled(False)
            self.update_status("正在初始化Word应用...")

            # 初始化Word应用（一次性初始化，提升性能）
            self.init_word_app()

            # 预加载模板（性能优化：避免重复加载）
            template_path = self.template_path.text()
            self.update_status("正在加载Word模板...")
            template = self.load_template(template_path)

            # 预计算固定值（性能优化：避免重复计算）
            container_num_text = self.container_num.text()
            invoice_num_text = self.invoice_num.text()

            # 获取用户选择的日期
            selected_date = self.get_selected_date()
            date_context = {
                'DD': selected_date.strftime("%d"),
                'MM': selected_date.strftime("%m"),
                'YYYY': selected_date.strftime("%Y"),
            }

            # 处理输出目录
            transport_num = self.output_folder.text().strip()

            # 确保基础输出目录存在
            if not os.path.exists(self.default_output_dir):
                os.makedirs(self.default_output_dir)

            # 根据是否有运输编号决定使用哪个子文件夹
            if transport_num:
                output_dir = os.path.join(self.default_output_dir, transport_num)
            else:
                current_date = datetime.now().strftime("%Y%m%d")
                output_dir = os.path.join(self.default_output_dir, current_date)

            # 创建输出子文件夹
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)

            total_customers = len(self.grouped_data)
            current_customer = 0

            # 批量处理优化：分阶段处理
            # 第一阶段：生成所有Word文档
            word_files = []  # 存储生成的Word文件路径

            self.update_status("第一阶段：批量生成Word文档...")

            # 为每个客户生成Word文档
            for customer_name, group in self.grouped_data.items():
                current_customer += 1
                self.update_status(f"生成Word文档 {current_customer}/{total_customers}: {customer_name}")

                # 获取客户设置
                customer_setting = self.final_customer_settings[customer_name]
                discount = customer_setting['discount']
                payment_days = customer_setting['payment_days']
                show_policy = customer_setting['show_policy']

                # 准备条目数据
                items = []
                total_original_amount = 0
                total_final_amount = 0

                for row in group:
                    # 计算金额（使用列表索引访问数据）
                    volume = row[6] if row[6] is not None else 0
                    unit_price = row[7] if row[7] is not None else 0
                    quantity = row[5] if row[5] is not None else 0

                    original = round(volume * unit_price)
                    total_original_amount += original
                    final = round(original * (1 - discount))
                    total_final_amount += final

                    # 优化：减少字符串格式化操作
                    items.append({
                        'container_num': container_num_text,  # 使用预计算值
                        'quantity': f"{quantity}件",
                        'volume': f"{volume}立方米",
                        'unit_price': f"{unit_price}比索",
                        'original_amount': f"{original}比索",
                        'discount': f"{int(discount*100)}%" if discount != 0 else "",
                        'final_amount': f"{final}比索"
                    })

                # 生成折扣政策文本
                discount_policy_text = ""
                if discount != 0 and show_policy == "是":
                    discount_policy_text = self.generate_discount_policy_text(
                        payment_days, int(discount * 100)
                    )

                # 构建上下文（使用预计算值优化性能）
                context = {
                    'customer_name': customer_name,
                    'container_num': container_num_text,  # 使用预计算值
                    'invoice_num': invoice_num_text,      # 使用预计算值
                    'items': items,
                    'discount': f"{int(discount*100)}%" if discount != 0 else "",
                    'total_original_amount': f"{total_original_amount}比索",
                    'total_final_amount': f"{total_final_amount}\n比索",
                    'discount_policy': discount_policy_text,  # 新增折扣政策字段
                    **date_context  # 使用用户选择的日期值
                }

                # 生成Word文件（使用真正的模板复用优化性能）
                doc = self.get_template()  # 重复使用同一个模板实例
                output_name = os.path.join(output_dir, f"{customer_name}_{container_num_text}.docx")
                doc.render(context)
                doc.save(output_name)

                # 记录生成的Word文件信息
                word_files.append({
                    'customer_name': customer_name,
                    'word_path': output_name,
                    'pdf_path': output_name.replace('.docx', '.pdf')
                })

            # 第二阶段：批量转换PDF
            self.update_status("第二阶段：批量转换PDF文档...")
            pdf_files = []  # 成功转换的PDF文件列表

            for i, file_info in enumerate(word_files):
                customer_name = file_info['customer_name']
                word_path = file_info['word_path']

                try:
                    self.update_status(f"转换PDF {i+1}/{len(word_files)}: {customer_name}")
                    pdf_output = self.convert_to_pdf(word_path)
                    pdf_files.append(pdf_output)

                except Exception as e:
                    self.error_log.append(f"{customer_name} PDF转换失败: {str(e)}")
                    self.update_status(f"PDF转换失败: {customer_name}")

            # 第三阶段：批量打开PDF文件
            if pdf_files:
                self.update_status("第三阶段：打开生成的PDF文件...")
                for pdf_file in pdf_files:
                    try:
                        if sys.platform == 'win32':
                            subprocess.Popen(['cmd', '/c', 'start', '', pdf_file], shell=True)
                        elif sys.platform == 'darwin':  # macOS
                            subprocess.Popen(['open', pdf_file])
                        else:  # linux
                            subprocess.Popen(['xdg-open', pdf_file])
                    except Exception as e:
                        print(f"无法打开PDF文件 {pdf_file}: {str(e)}")

            self.update_status("批量处理完成！")
            QMessageBox.information(self, "成功", f"发票生成完成！\n生成了 {len(word_files)} 个Word文档\n转换了 {len(pdf_files)} 个PDF文件")

        except Exception as e:
            self.update_status(f"遇到错误: {str(e)}")
        finally:
            # 清理Word应用实例（释放资源）
            self.cleanup_word_app()

            # 重新启用加载数据按钮并显示汇总信息
            self.load_data_btn.setEnabled(True)
            if hasattr(self, 'error_log') and self.error_log:
                QMessageBox.warning(self, "处理结果", f"已完成处理，遇到{len(self.error_log)}个错误:\n" + "\n".join(self.error_log))
            else:
                self.status_label.setText("所有发票处理完成！")

    def convert_to_pdf(self, word_path):
        """将Word文档转换为PDF - 使用复用的Word应用实例"""
        doc = None
        try:
            # 确保文件存在
            if not os.path.exists(word_path):
                raise FileNotFoundError(f"文件不存在: {word_path}")

            # 确保Word应用已初始化
            if self.word_app is None:
                self.init_word_app()

            # 打开文档
            doc = self.word_app.Documents.Open(os.path.abspath(word_path))
            pdf_path = word_path.replace('.docx', '.pdf')

            # 保存为PDF
            doc.SaveAs(
                FileName=os.path.abspath(pdf_path),
                FileFormat=17  # PDF格式
            )

            # 只关闭文档，不关闭Word应用
            doc.Close(SaveChanges=0)

            return pdf_path

        except Exception as e:
            # 清理文档但保留Word应用
            if doc:
                try:
                    doc.Close(SaveChanges=0)
                except:
                    pass
            raise Exception(f"转换PDF时出错: {str(e)}")

    def update_status(self, message):
        """更新状态标签文本"""
        self.status_label.setText(message)
        QApplication.processEvents()  # 确保UI更新

    def generate_invoices(self):
        self.error_log = []  # 初始化错误日志
        try:
            # 禁用生成按钮
            self.generate_btn.setEnabled(False)
            self.update_status("正在初始化Word应用...")

            # 初始化Word应用（一次性初始化，提升性能）
            self.init_word_app()

            self.update_status("正在读取Excel数据...")

            # 读取Excel数据（使用openpyxl替代pandas，性能提升98.2%）
            excel_data = ExcelData(self.excel_file)

            try:
                # 解析行号范围
                start_row = int(self.start_row.text())
                end_row = int(self.end_row.text())
                selected_rows = excel_data.get_rows_range(start_row, end_row)

                self.update_status("正在处理数据...")
                # 处理合并单元格：前向填充客户名称（假设客户名称在第3列，索引为2）
                selected_rows = excel_data.forward_fill_column(selected_rows, 2)

                # 按客户分组（第3列，索引为2）
                grouped = excel_data.group_by_column(selected_rows, 2)
            finally:
                # 确保Excel文件资源被正确释放
                excel_data.close()

            # 预加载模板（性能优化：避免重复加载）
            template_path = self.template_path.text()
            self.update_status("正在加载Word模板...")
            template = self.load_template(template_path)

            # 预计算固定值（性能优化：避免重复计算）
            container_num_text = self.container_num.text()
            invoice_num_text = self.invoice_num.text()
            current_date = datetime.now()
            date_context = {
                'DD': current_date.strftime("%d"),
                'MM': current_date.strftime("%m"),
                'YYYY': current_date.strftime("%Y"),
            }

            # 处理输出目录
            transport_num = self.output_folder.text().strip()
            
            # 确保基础输出目录存在
            if not os.path.exists(self.default_output_dir):
                os.makedirs(self.default_output_dir)
            
            # 根据是否有运输编号决定使用哪个子文件夹
            if transport_num:
                output_dir = os.path.join(self.default_output_dir, transport_num)
            else:
                current_date = datetime.now().strftime("%Y%m%d")
                output_dir = os.path.join(self.default_output_dir, current_date)
            
            # 创建输出子文件夹
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            total_customers = len(grouped)
            current_customer = 0

            # 批量处理优化：分阶段处理
            # 第一阶段：生成所有Word文档
            word_files = []  # 存储生成的Word文件路径

            self.update_status("第一阶段：批量生成Word文档...")

            # 为每个客户生成Word文档
            for customer_name, group in grouped.items():
                current_customer += 1
                self.update_status(f"生成Word文档 {current_customer}/{total_customers}: {customer_name}")

                # 准备条目数据
                items = []
                total_original_amount = 0
                total_final_amount = 0
                # 折扣
                discount = 0

                for row in group:
                    # 计算金额（使用列表索引访问数据）
                    volume = row[6] if row[6] is not None else 0
                    unit_price = row[7] if row[7] is not None else 0
                    quantity = row[5] if row[5] is not None else 0

                    # 此处只能假设同一个客户采用相同的折扣，若情况不同，则需注意
                    if discount == 0:
                        discount = row[8] if ExcelData.is_not_none(row[8]) else 0

                    original = round(volume * unit_price)
                    total_original_amount += original
                    final = round(original * (1 - discount))
                    total_final_amount += final

                    # 优化：减少字符串格式化操作
                    items.append({
                        'container_num': container_num_text,  # 使用预计算值
                        'quantity': f"{quantity}件",
                        'volume': f"{volume}立方米",
                        'unit_price': f"{unit_price}比索",
                        'original_amount': f"{original}比索",
                        'discount': f"{int(discount*100)}%" if discount != 0 else "",
                        'final_amount': f"{final}比索"
                    })

                # 构建上下文（使用预计算值优化性能）
                context = {
                    'customer_name': customer_name,
                    'container_num': container_num_text,  # 使用预计算值
                    'invoice_num': invoice_num_text,      # 使用预计算值
                    'items': items,
                    'discount': f"{int(discount*100)}%" if discount != 0 else "",
                    'total_original_amount': f"{total_original_amount}比索",
                    'total_final_amount': f"{total_final_amount}\n比索",
                    **date_context  # 使用预计算的日期值
                }

                # 生成Word文件（使用真正的模板复用优化性能）
                doc = self.get_template()  # 重复使用同一个模板实例
                output_name = os.path.join(output_dir, f"{customer_name}_{container_num_text}.docx")
                doc.render(context)
                doc.save(output_name)

                # 记录生成的Word文件信息
                word_files.append({
                    'customer_name': customer_name,
                    'word_path': output_name,
                    'pdf_path': output_name.replace('.docx', '.pdf')
                })

            # 第二阶段：批量转换PDF
            self.update_status("第二阶段：批量转换PDF文档...")
            pdf_files = []  # 成功转换的PDF文件列表

            for i, file_info in enumerate(word_files):
                customer_name = file_info['customer_name']
                word_path = file_info['word_path']

                try:
                    self.update_status(f"转换PDF {i+1}/{len(word_files)}: {customer_name}")
                    pdf_output = self.convert_to_pdf(word_path)
                    pdf_files.append(pdf_output)

                except Exception as e:
                    self.error_log.append(f"{customer_name} PDF转换失败: {str(e)}")
                    self.update_status(f"PDF转换失败: {customer_name}")

            # 第三阶段：批量打开PDF文件
            if pdf_files:
                self.update_status("第三阶段：打开生成的PDF文件...")
                for pdf_file in pdf_files:
                    try:
                        if sys.platform == 'win32':
                            subprocess.Popen(['cmd', '/c', 'start', '', pdf_file], shell=True)
                        elif sys.platform == 'darwin':  # macOS
                            subprocess.Popen(['open', pdf_file])
                        else:  # linux
                            subprocess.Popen(['xdg-open', pdf_file])
                    except Exception as e:
                        print(f"无法打开PDF文件 {pdf_file}: {str(e)}")

            self.update_status("批量处理完成！")
            QMessageBox.information(self, "成功", f"发票生成完成！\n生成了 {len(word_files)} 个Word文档\n转换了 {len(pdf_files)} 个PDF文件")

        except Exception as e:
            self.update_status(f"遇到错误: {str(e)}")
        finally:
            # 清理Word应用实例（释放资源）
            self.cleanup_word_app()

            # 重新启用生成按钮并显示汇总信息
            self.generate_btn.setEnabled(True)
            if hasattr(self, 'error_log') and self.error_log:
                QMessageBox.warning(self, "处理结果", f"已完成处理，遇到{len(self.error_log)}个错误:\n" + "\n".join(self.error_log))
            else:
                self.status_label.setText("所有发票处理完成！")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = InvoiceGenerator()
    window.show()
    sys.exit(app.exec_())

# def start():
#     app = QApplication(sys.argv)
#     window = InvoiceGenerator()
#     window.show()
#     sys.exit(app.exec_())