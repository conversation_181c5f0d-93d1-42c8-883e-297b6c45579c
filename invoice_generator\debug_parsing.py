#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试解析过程
"""

def debug_parsing():
    """调试解析过程"""
    print("🔍 调试解析过程")
    print("=" * 50)
    
    # 真实的剪贴板数据
    test_data = "盼盼\t普货\tPANGPANG\t1\t0.33\t6500\t5%\n生生\t普货\tLUXIAO\t31\t2.22\t7000\t\nPANDA\t铝膜球\tPANDA\t360\t11\t7500\t3%\n\t普货\tPANDA\t96\t5.3\t7600\t3%\n\t电器\tPANDA\t25\t1.5\t8000\t3%"
    
    lines = test_data.strip().split('\n')
    
    for i, line in enumerate(lines):
        print(f"\n处理第{i+1}行:")
        print(f"  原始: {repr(line)}")
        
        # 不要对整行使用strip()，以保留开头的制表符
        print(f"  保持原样: {repr(line)}")

        if '\t' in line:
            row = line.split('\t')
        elif ',' in line:
            row = line.split(',')
        else:
            row = line.strip().split()
        
        print(f"  分隔后: {row}")
        
        row = [cell.strip() for cell in row]
        print(f"  清理后: {row}")
        
        # 确保每行都有7列
        while len(row) < 7:
            row.append('')
        
        # 只保留前7列
        row = row[:7]
        print(f"  标准化: {row}")
        
        # 处理折扣列
        if len(row) > 6 and row[6]:
            discount_str = row[6].strip()
            print(f"  折扣原始: '{discount_str}'")
            
            if discount_str.endswith('%'):
                try:
                    discount_value = float(discount_str[:-1]) / 100
                    row[6] = str(discount_value)
                    print(f"  折扣转换: '{discount_str}' -> '{row[6]}'")
                except ValueError:
                    row[6] = '0'
                    print(f"  折扣转换失败: '{discount_str}' -> '0'")
            elif discount_str and discount_str != '0':
                try:
                    float(discount_str)
                    print(f"  折扣保持: '{discount_str}'")
                except ValueError:
                    row[6] = '0'
                    print(f"  折扣无效: '{discount_str}' -> '0'")
        else:
            print(f"  折扣为空: '{row[6]}'")
        
        print(f"  最终结果: {row}")

if __name__ == "__main__":
    debug_parsing()
