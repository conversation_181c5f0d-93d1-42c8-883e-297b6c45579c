# 发票生成器业务变动说明

## 概述
本次更新实现了发票生成器的重要业务变动，包括数据加载流程改进、账期管理、折扣政策提示等新功能。

## 主要变更

### 1. 数据加载流程变更
- **原流程**：用户填完信息后直接点击"生成发票"
- **新流程**：用户填完信息后先点击"加载数据"，弹出数据预览对话框进行设置确认

### 2. 新增功能

#### 2.1 日期选择功能
- 支持使用当前日期或自定义日期
- 用于处理补开发票的情况
- 位置：主界面

#### 2.2 客户账期管理
- 自动创建和管理 `customer_payment_terms.xlsx` 文件
- 包含3列：客户名称、账期天数、折扣政策提示（"是"/"否"）
- 默认值：账期7天，折扣政策提示"是"
- 每次确认设置后自动更新文件

#### 2.3 数据预览对话框
显示内容：
- **客户**：不可编辑，同客户条目仅显示一次
- **折扣**：不可编辑，从Excel数据读取
- **账期**：可编辑，仅当客户有折扣时有效
- **折扣政策提示**：勾选框，仅当客户有折扣时有效

业务逻辑：
- 无折扣客户：账期和政策提示控件禁用，政策提示默认"否"
- 有折扣客户：可编辑账期，可选择是否显示政策提示

#### 2.4 折扣政策字段
- 模板新增 `{{discount_policy}}` 字段
- 填充条件：折扣不为0% **且** 折扣政策提示被勾选
- 填充内容：`"遵循公司最新优惠政策，收到货后\n\n{账期}天内付款此单享有{折扣}%的折扣。"`

## 技术实现

### 新增类和方法

#### CustomerPaymentTerms 类
- `__init__(file_path)`: 初始化账期管理
- `ensure_file_exists()`: 确保账期文件存在
- `get_customer_terms(customer_name)`: 获取客户账期设置
- `update_customer_terms(customer_data)`: 更新客户账期设置

#### DataPreviewDialog 类
- 继承自 QDialog
- 显示客户数据预览表格
- 支持账期编辑和政策提示设置
- 自动处理有/无折扣客户的不同逻辑

#### InvoiceGenerator 新增方法
- `on_date_option_changed()`: 处理日期选择变更
- `get_selected_date()`: 获取用户选择的日期
- `load_data()`: 加载数据并显示预览对话框
- `show_data_preview_dialog()`: 显示数据预览对话框
- `generate_discount_policy_text()`: 生成折扣政策文本
- `generate_invoices_with_settings()`: 使用设置生成发票

## 文件结构
```
invoice_generator/
├── invoice_generator.py          # 主程序（已更新）
├── customer_payment_terms.xlsx   # 客户账期管理文件（自动创建）
├── test_changes.py              # 测试脚本
├── README_CHANGES.md            # 本文档
└── invoice_template.docx        # 发票模板（需添加{{discount_policy}}字段）
```

## 使用说明

### 基本流程
1. 选择Excel数据源和Word模板
2. 填写柜号、发票号、行号范围
3. 选择发票日期（当前日期或自定义）
4. 填写运输编号（可选）
5. 点击"加载数据"
6. 在弹出的对话框中检查和设置客户账期信息
7. 点击"确认并生成发票"

### 注意事项
- 账期和折扣政策提示仅对有折扣的客户有效
- 无折扣客户的政策提示会自动设为"否"
- 账期设置会自动保存，下次使用时无需重复设置
- 模板中需要包含 `{{discount_policy}}` 字段

## 测试
运行测试脚本验证功能：
```bash
python test_changes.py
```

测试覆盖：
- 客户账期管理功能
- 折扣政策文本生成
- 日期选择功能

## 兼容性
- 保持与原有Excel数据格式的兼容性
- 保持与原有模板字段的兼容性
- 新增功能不影响现有工作流程
