#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试折扣解析问题
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

def test_discount_parsing():
    """测试折扣解析问题"""
    print("🔍 测试折扣解析问题")
    print("=" * 50)
    
    # 用户提供的原始数据
    test_data = """盼盼	普货	PANGPANG	1	0.33	6500	5%
生生	普货	LUXIAO	31	2.22	7000	
PANDA	铝膜球	PANDA	360	11	7500	3%
	普货	PANDA	96	5.3	7600	3%
	电器	PANDA	25	1.5	8000	3%"""
    
    print("原始数据：")
    print(test_data)
    print()
    
    # 模拟剪贴板解析过程
    lines = test_data.strip().split('\n')
    parsed_data = []
    
    for i, line in enumerate(lines):
        line = line.strip()
        if not line:
            continue
        
        print(f"处理第{i+1}行: '{line}'")
        
        # 尝试制表符分隔
        if '\t' in line:
            row = line.split('\t')
            print(f"  制表符分隔结果: {row}")
        elif ',' in line:
            row = line.split(',')
            print(f"  逗号分隔结果: {row}")
        else:
            row = line.split()
            print(f"  空格分隔结果: {row}")
        
        # 清理每个单元格的数据
        row = [cell.strip() for cell in row]
        print(f"  清理后: {row}")
        print(f"  列数: {len(row)}")
        
        # 检查折扣列（第7列，索引6）
        if len(row) > 6:
            discount_raw = row[6]
            print(f"  原始折扣值: '{discount_raw}'")
            
            # 处理折扣值
            if discount_raw:
                if discount_raw.endswith('%'):
                    try:
                        discount_num = float(discount_raw[:-1]) / 100
                        print(f"  转换后折扣: {discount_num}")
                    except ValueError:
                        print(f"  折扣转换失败: '{discount_raw}'")
                        discount_num = 0
                else:
                    try:
                        discount_num = float(discount_raw)
                        print(f"  直接转换折扣: {discount_num}")
                    except ValueError:
                        print(f"  折扣转换失败: '{discount_raw}'")
                        discount_num = 0
            else:
                discount_num = 0
                print(f"  空折扣，设为: {discount_num}")
        else:
            print(f"  没有折扣列")
            discount_num = 0
        
        parsed_data.append(row)
        print()
    
    print("解析结果总结：")
    for i, row in enumerate(parsed_data):
        customer = row[0] if len(row) > 0 else "无客户"
        discount_raw = row[6] if len(row) > 6 else "无折扣"
        print(f"  行{i+1}: 客户='{customer}', 原始折扣='{discount_raw}'")
    
    return parsed_data

def test_current_implementation():
    """测试当前实现的问题"""
    print("\n🔧 测试当前实现")
    print("=" * 50)
    
    from PyQt5.QtWidgets import QApplication
    app = QApplication([])
    
    try:
        from invoice_generator import InvoiceGenerator
        
        generator = InvoiceGenerator()
        
        # 模拟剪贴板数据
        test_data = """盼盼	普货	PANGPANG	1	0.33	6500	5%
生生	普货	LUXIAO	31	2.22	7000	
PANDA	铝膜球	PANDA	360	11	7500	3%
	普货	PANDA	96	5.3	7600	3%
	电器	PANDA	25	1.5	8000	3%"""
        
        # 解析数据（模拟read_clipboard_data的逻辑）
        lines = test_data.strip().split('\n')
        parsed_data = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            if '\t' in line:
                row = line.split('\t')
            elif ',' in line:
                row = line.split(',')
            else:
                row = line.split()
            
            row = [cell.strip() for cell in row]
            parsed_data.append(row)
        
        generator.clipboard_data = parsed_data
        
        # 测试数据处理
        clipboard_rows = generator._load_clipboard_data()
        filled_rows = generator._forward_fill_column(clipboard_rows.copy(), 0)
        grouped_data = generator._group_by_column(filled_rows, 0)
        
        print("分组结果：")
        for customer, rows in grouped_data.items():
            print(f"客户: '{customer}'")
            for row in rows:
                discount_raw = row[6] if len(row) > 6 else "无"
                print(f"  折扣原始值: '{discount_raw}'")
                
                # 测试折扣转换
                try:
                    discount = float(discount_raw)
                    print(f"  转换为: {discount} ({discount*100}%)")
                except (ValueError, TypeError):
                    print(f"  转换失败: '{discount_raw}'")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_discount_parsing()
    test_current_implementation()
