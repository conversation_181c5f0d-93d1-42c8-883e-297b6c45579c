#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试：验证折扣解析修复
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

def test_final_discount_parsing():
    """最终测试折扣解析"""
    print("🎯 最终测试：折扣解析修复")
    print("=" * 50)
    
    from PyQt5.QtWidgets import QApplication
    app = QApplication([])
    
    try:
        from invoice_generator import InvoiceGenerator, DataPreviewDialog, CustomerPaymentTerms
        
        generator = InvoiceGenerator()
        
        # 真实的剪贴板数据格式
        test_data = "盼盼\t普货\tPANGPANG\t1\t0.33\t6500\t5%\n生生\t普货\tLUXIAO\t31\t2.22\t7000\t\nPANDA\t铝膜球\tPANDA\t360\t11\t7500\t3%\n\t普货\tPANDA\t96\t5.3\t7600\t3%\n\t电器\tPANDA\t25\t1.5\t8000\t3%"
        
        print("测试数据：")
        for i, line in enumerate(test_data.split('\n')):
            print(f"  行{i+1}: {repr(line)}")
        print()
        
        # 模拟剪贴板解析过程（使用实际的解析逻辑）
        lines = test_data.strip().split('\n')
        parsed_data = []
        
        for line in lines:
            # 不要对整行使用strip()，以保留开头的制表符
            if not line:
                continue

            if '\t' in line:
                row = line.split('\t')
            elif ',' in line:
                row = line.split(',')
            else:
                row = line.strip().split()
            
            row = [cell.strip() for cell in row]
            
            # 确保每行都有7列
            while len(row) < 7:
                row.append('')
            
            # 只保留前7列
            row = row[:7]
            
            # 处理折扣列
            if len(row) > 6 and row[6]:
                discount_str = row[6].strip()
                if discount_str.endswith('%'):
                    try:
                        discount_value = float(discount_str[:-1]) / 100
                        row[6] = str(discount_value)
                    except ValueError:
                        row[6] = '0'
                elif discount_str and discount_str != '0':
                    try:
                        float(discount_str)
                    except ValueError:
                        row[6] = '0'
            
            parsed_data.append(row)
        
        print("解析结果：")
        for i, row in enumerate(parsed_data):
            customer = row[0] if row[0] else "[空客户]"
            discount = row[6] if row[6] else "0"
            print(f"  行{i+1}: 客户='{customer}', 折扣='{discount}'")
        print()
        
        # 设置剪贴板数据并处理
        generator.clipboard_data = parsed_data
        
        clipboard_rows = generator._load_clipboard_data()
        filled_rows = generator._forward_fill_column(clipboard_rows.copy(), 0)
        grouped_data = generator._group_by_column(filled_rows, 0)
        
        print("分组和前向填充结果：")
        for customer, rows in grouped_data.items():
            print(f"客户: '{customer}'")
            for j, row in enumerate(rows):
                discount_raw = row[6] if len(row) > 6 else "0"
                try:
                    discount_num = float(discount_raw)
                    print(f"  行{j+1}: 折扣={discount_raw} ({discount_num*100}%)")
                except ValueError:
                    print(f"  行{j+1}: 折扣='{discount_raw}' (无效)")
        print()
        
        # 测试DataPreviewDialog
        print("测试DataPreviewDialog:")
        payment_terms = CustomerPaymentTerms()
        dialog = DataPreviewDialog(grouped_data, payment_terms)
        
        print("客户折扣信息:")
        for customer, rows in grouped_data.items():
            for data_row in rows:
                discount_col_index = dialog._get_discount_column_index(data_row)
                if discount_col_index < len(data_row):
                    discount_raw = data_row[discount_col_index]
                    try:
                        discount = float(discount_raw)
                        discount_text = f"{int(discount*100)}%" if discount != 0 else "无折扣"
                        print(f"  {customer}: {discount_text}")
                        break
                    except (ValueError, TypeError):
                        print(f"  {customer}: 折扣解析失败 ('{discount_raw}')")
                        break
        
        print("\n✅ 最终测试完成！")
        print("\n预期结果：")
        print("  盼盼: 5%")
        print("  生生: 无折扣")
        print("  PANDA: 3%")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_final_discount_parsing()
