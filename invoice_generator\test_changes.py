#!/usr/bin/env python3
"""
测试发票生成器的新功能
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from invoice_generator import CustomerPaymentTerms, DataPreviewDialog, InvoiceGenerator
from PyQt5.QtWidgets import QApplication

def test_customer_payment_terms():
    """测试客户账期管理功能"""
    print("测试客户账期管理功能...")
    
    # 创建测试文件路径
    test_file = "test_customer_payment_terms.xlsx"
    
    # 清理可能存在的测试文件
    if os.path.exists(test_file):
        os.remove(test_file)
    
    # 创建CustomerPaymentTerms实例
    cpt = CustomerPaymentTerms(test_file)
    
    # 测试获取不存在的客户（应返回默认值）
    payment_days, show_policy = cpt.get_customer_terms("测试客户1")
    assert payment_days == 7, f"期望默认账期为7天，实际为{payment_days}"
    assert show_policy == "是", f"期望默认政策提示为'是'，实际为'{show_policy}'"
    print("✓ 默认值测试通过")
    
    # 测试更新客户设置
    test_data = {
        "测试客户1": (15, "是"),
        "测试客户2": (30, "否"),
        "测试客户3": (7, "是")
    }
    
    cpt.update_customer_terms(test_data)
    print("✓ 客户设置更新完成")
    
    # 测试读取更新后的设置
    for customer, (expected_days, expected_policy) in test_data.items():
        actual_days, actual_policy = cpt.get_customer_terms(customer)
        assert actual_days == expected_days, f"客户{customer}账期不匹配：期望{expected_days}，实际{actual_days}"
        assert actual_policy == expected_policy, f"客户{customer}政策提示不匹配：期望{expected_policy}，实际{actual_policy}"
    
    print("✓ 客户设置读取测试通过")
    
    # 清理测试文件
    if os.path.exists(test_file):
        os.remove(test_file)
    
    print("✓ 客户账期管理功能测试完成")

def test_discount_policy_text():
    """测试折扣政策文本生成"""
    print("测试折扣政策文本生成...")
    
    app = QApplication([])
    generator = InvoiceGenerator()
    
    # 测试折扣政策文本生成
    text = generator.generate_discount_policy_text(15, 5)
    expected = "遵循公司最新优惠政策，收到货后\n\n15天内付款此单享有5%的折扣。"
    assert text == expected, f"折扣政策文本不匹配：\n期望：{expected}\n实际：{text}"
    
    print("✓ 折扣政策文本生成测试通过")
    
    app.quit()

def test_date_selection():
    """测试日期选择功能"""
    print("测试日期选择功能...")
    
    app = QApplication([])
    generator = InvoiceGenerator()
    
    # 测试当前日期选择（默认）
    selected_date = generator.get_selected_date()
    from datetime import datetime
    current_date = datetime.now()
    
    # 检查是否为当前日期（允许几秒的误差）
    time_diff = abs((selected_date - current_date).total_seconds())
    assert time_diff < 60, f"当前日期选择有误，时间差：{time_diff}秒"
    
    print("✓ 当前日期选择测试通过")
    
    # 测试自定义日期选择
    from PyQt5.QtCore import QDate
    test_date = QDate(2024, 1, 15)
    generator.custom_date_radio.setChecked(True)
    generator.custom_date_edit.setDate(test_date)
    
    selected_date = generator.get_selected_date()
    assert selected_date.year == 2024, f"年份不匹配：期望2024，实际{selected_date.year}"
    assert selected_date.month == 1, f"月份不匹配：期望1，实际{selected_date.month}"
    assert selected_date.day == 15, f"日期不匹配：期望15，实际{selected_date.day}"
    
    print("✓ 自定义日期选择测试通过")
    
    app.quit()

def main():
    """运行所有测试"""
    print("开始测试发票生成器的新功能...\n")
    
    try:
        test_customer_payment_terms()
        print()
        
        test_discount_policy_text()
        print()
        
        test_date_selection()
        print()
        
        print("✅ 所有测试通过！")
        
    except Exception as e:
        print(f"❌ 测试失败：{e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
